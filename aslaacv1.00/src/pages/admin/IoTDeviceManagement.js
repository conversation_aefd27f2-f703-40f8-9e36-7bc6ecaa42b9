import React, { useState, useEffect, use<PERSON>allback, useMemo } from 'react';
import {
  Container,
  Card,
  CardContent,
  Typography,
  Grid,
  Button,
  TextField,
  Box,
  Chip,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Paper,
  IconButton,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Alert,
  CircularProgress,
  Tooltip,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  InputAdornment,
  Divider,
  Stack,
  Pagination,
  Tabs,
  Tab
} from '@mui/material';
import { LoadingButton } from '@mui/lab';
import {
  Refresh as RefreshIcon,
  Search as SearchIcon,
  Clear as ClearIcon,
  PowerSettingsNew as RestartIcon,
  SystemUpdate as UpdateIcon,
  SimCard as SimIcon,
  CheckCircle as OnlineIcon,
  Cancel as OfflineIcon,
  Send as SendIcon,
  History as HistoryIcon,
  CheckCircleOutline as CheckIcon
} from '@mui/icons-material';

// Components
import Page from '../../components/Page';
import Layout from '../../layout';
import Iconify from '../../components/Iconify';
import Scrollbar from '../../components/Scrollbar';

// Services
import mqttService from '../../services/mqttService';
import axios from '../../utils/axios';
import { useSnackbar } from 'notistack';

// Utils
import { formatDistanceToNow } from 'date-fns';

// Helper function to extract version from payload
const extractVersionFromPayload = (payload) => {
  if (!payload) return 'Unknown';
  try {
    const parsed = JSON.parse(payload);
    return parsed.ver || parsed.version || parsed.firmware || parsed.fw || 'Unknown';
  } catch (e) {
    // If JSON parsing fails, try regex to extract version
    const versionMatch = payload.match(/"ver":"([^"]+)"/);
    if (versionMatch) return versionMatch[1];
    return 'Unknown';
  }
};

// Helper function to categorize version
const categorizeVersion = (version) => {
  if (!version || version === 'Unknown') return 'unknown';

  // Parse version numbers (e.g., "1.2.6", "004.000.000")
  const versionMatch = version.match(/(\d+)\.(\d+)\.(\d+)/);
  if (!versionMatch) return 'unknown';

  const [, major, minor, patch] = versionMatch.map(Number);

  // Consider versions 1.4.0+ as current, 1.2.6-1.3.x as old
  if (major > 1 || (major === 1 && minor >= 4)) {
    return 'current';
  } else if (major === 1 && minor >= 2) {
    return 'old';
  }

  return 'unknown';
};

// Helper function to categorize last log time
const categorizeLastLog = (lastLogTime) => {
  if (!lastLogTime) return 'no_log';

  const logDate = new Date(lastLogTime);
  const now = new Date();
  const hoursDiff = (now - logDate) / (1000 * 60 * 60);

  if (hoursDiff <= 24) return 'recent'; // Last 24 hours
  if (hoursDiff <= 168) return 'old'; // Last week
  return 'no_log'; // Older than a week
};

// Helper function to calculate remaining days
const calculateRemainingDays = (expiredDate) => {
  if (!expiredDate) return null;

  const now = new Date();
  const expiry = new Date(expiredDate);
  const diffTime = expiry - now;
  const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));

  return diffDays;
};

// Helper function to get remaining days display
const getRemainingDaysDisplay = (expiredDate) => {
  const remainingDays = calculateRemainingDays(expiredDate);

  if (remainingDays === null) return { text: 'Unknown', color: 'default', severity: 'info' };

  if (remainingDays < 0) {
    return {
      text: `Expired ${Math.abs(remainingDays)} days ago`,
      color: 'error',
      severity: 'error'
    };
  } else if (remainingDays === 0) {
    return {
      text: 'Expires today',
      color: 'error',
      severity: 'warning'
    };
  } else if (remainingDays <= 7) {
    return {
      text: `${remainingDays} days left`,
      color: 'warning',
      severity: 'warning'
    };
  } else if (remainingDays <= 30) {
    return {
      text: `${remainingDays} days left`,
      color: 'info',
      severity: 'info'
    };
  } else {
    return {
      text: `${remainingDays} days left`,
      color: 'success',
      severity: 'success'
    };
  }
};

const IoTDeviceManagement = () => {
  // Hooks
  const { enqueueSnackbar } = useSnackbar();

  // State management
  const [devices, setDevices] = useState([]);
  const [loading, setLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState('');
  const [inputValue, setInputValue] = useState(''); // Separate input state for immediate UI updates
  const [selectedDevice, setSelectedDevice] = useState(null);
  const [commandDialog, setCommandDialog] = useState(false);
  const [commandType, setCommandType] = useState('');
  const [commandLoading, setCommandLoading] = useState(false);
  const [dialogTab, setDialogTab] = useState(0); // 0 = Command, 1 = History
  const [commandHistory, setCommandHistory] = useState([]);
  const [deviceResponses, setDeviceResponses] = useState({});
  const [mqttConnected, setMqttConnected] = useState(false);
  const [onlineDevices, setOnlineDevices] = useState(new Set());

  // Pagination states
  const [currentPage, setCurrentPage] = useState(1);
  const [pageSize, setPageSize] = useState(20);
  const [totalPages, setTotalPages] = useState(0);
  const [totalDevices, setTotalDevices] = useState(0);
  const [lastStatusUpdate, setLastStatusUpdate] = useState(null);

  // Command specific states
  const [simCommand, setSimCommand] = useState('');
  const [customCommand, setCustomCommand] = useState('');

  // User management dialog states
  const [userManageDialog, setUserManageDialog] = useState(false);
  const [selectedUser, setSelectedUser] = useState(null);
  const [isResettingPin, setIsResettingPin] = useState(false);
  const [isExtendingExpiry, setIsExtendingExpiry] = useState(false);
  const [daysToAdd, setDaysToAdd] = useState('');

  // Create user dialog states
  const [createUserDialog, setCreateUserDialog] = useState(false);
  const [newUserPhone, setNewUserPhone] = useState('');
  const [isCreatingUser, setIsCreatingUser] = useState(false);

  // Device management dialog states
  const [deviceManageDialog, setDeviceManageDialog] = useState(false);
  const [selectedDeviceForEdit, setSelectedDeviceForEdit] = useState(null);
  const [isEditingDevice, setIsEditingDevice] = useState(false);
  const [isDeletingDevice, setIsDeletingDevice] = useState(false);
  const [deviceEditForm, setDeviceEditForm] = useState({
    deviceNumber: '',
    type: '4g',
    uix: 'CarV1.2'
  });

  // Filter states
  const [userStatusFilter, setUserStatusFilter] = useState('all'); // 'all', 'active', 'expired'
  const [onlineFilter, setOnlineFilter] = useState('all'); // 'all', 'online', 'offline'
  const [versionFilter, setVersionFilter] = useState('all'); // 'all', 'unknown', 'old', 'current'
  const [lastLogFilter, setLastLogFilter] = useState('all'); // 'all', 'no_log', 'old', 'recent'
  const [remainingDaysFilter, setRemainingDaysFilter] = useState('all'); // 'all', 'expired', 'expiring_soon', 'active'

  // MQTT connection and setup
  useEffect(() => {
    const initializeMqtt = async () => {
      try {
        // MQTT broker fallback list - using same config as working admin statistics
        const brokerUrls = [
          'wss://elec.mn:8084/mqtt',      // Primary (secure WebSocket)
          'wss://api.elec.mn:8084/mqtt',  // Secondary
          'wss://bot.elec.mn:8084/mqtt',  // Tertiary
          'wss://app.elec.mn:8084/mqtt'   // Quaternary
        ];

        let brokerUrl = brokerUrls[0]; // Start with primary

        console.log('IoT Management: Initializing MQTT connection to:', brokerUrl);
        console.log('IoT Management: Fallback brokers available:', brokerUrls.slice(1));
        console.log('IoT Management: Using same config as working admin statistics (no auth)');

        const connected = mqttService.connect(brokerUrl, {
          clientId: `admin_${Date.now()}`
        });
        
        // Set up event handlers
        let currentBrokerIndex = 0;

        const tryNextBroker = () => {
          currentBrokerIndex = (currentBrokerIndex + 1) % brokerUrls.length;
          const nextBrokerUrl = brokerUrls[currentBrokerIndex];
          console.log(`IoT Management: Trying next broker: ${nextBrokerUrl}`);

          return mqttService.connect(nextBrokerUrl, {
            clientId: `admin_${Date.now()}`
          });
        };

        mqttService.on('connect', () => {
          console.log('IoT Management: MQTT Connected successfully to:', brokerUrls[currentBrokerIndex]);
          console.log('IoT Management: Service isConnected:', mqttService.isConnected);
          setMqttConnected(true);
        });

        mqttService.on('disconnect', () => {
          console.log('IoT Management: MQTT Disconnected from:', brokerUrls[currentBrokerIndex]);
          setMqttConnected(false);
        });

        mqttService.on('error', (error) => {
          console.error('IoT Management: MQTT Error with broker:', brokerUrls[currentBrokerIndex], error);
          setMqttConnected(false);

          // Try next broker after a short delay
          setTimeout(() => {
            if (!mqttConnected && currentBrokerIndex < brokerUrls.length - 1) {
              console.log('IoT Management: Attempting fallback broker...');
              tryNextBroker();
            }
          }, 2000);
        });

        mqttService.on('message', (topic, message) => {
          handleDeviceResponse(topic, message);
        });

        if (connected) {
          console.log('IoT Management: MQTT connection initiated successfully');
        } else {
          console.error('IoT Management: Failed to initialize MQTT connection, trying fallback...');
          tryNextBroker();
        }
      } catch (error) {
        console.error('IoT Management: MQTT connection failed:', error);
      }
    };

    initializeMqtt();
    
    return () => {
      mqttService.disconnect();
    };
  }, []);

  // Handle device responses
  const handleDeviceResponse = useCallback((topic, message) => {
    try {
      // Check if this is a device response (deviceNumber/msg format)
      if (topic.includes('/msg')) {
        const deviceNumber = topic.replace('/msg', '');
        const response = typeof message === 'string' ? message : message.toString();
        
        setDeviceResponses(prev => ({
          ...prev,
          [deviceNumber]: {
            message: response,
            timestamp: new Date(),
            topic
          }
        }));
        
        // Add to command history
        setCommandHistory(prev => [{
          id: Date.now(),
          deviceNumber,
          type: 'response',
          message: response,
          timestamp: new Date(),
          success: true
        }, ...prev.slice(0, 99)]); // Keep last 100 entries
      }
    } catch (error) {
      console.error('IoT Management: Error handling device response:', error);
    }
  }, []);

  // Load devices with pagination
  const loadDevices = useCallback(async (page = currentPage, search = searchTerm) => {
    try {
      setLoading(true);
      const response = await axios.post('/api/device/list/all', {
        page,
        limit: pageSize,
        search
      });

      if (response.data.success && response.data.list) {
        const deviceList = response.data.list.map(device => ({
          ...device,
          lastSeen: device.lastPayload ? new Date(device.lastPayload) : null,
          isOnline: false, // Will be updated by MQTT status check
          userInfo: device.user && device.user.length > 0 ? device.user[0] : null
        }));

        setDevices(deviceList);

        // Update pagination info
        if (response.data.pagination) {
          setTotalPages(response.data.pagination.totalPages);
          setTotalDevices(response.data.pagination.totalDevices);
          setCurrentPage(response.data.pagination.currentPage);
        }

        // Check online status for each device
        checkDevicesOnlineStatus(deviceList);
      }
    } catch (error) {
      console.error('IoT Management: Error loading devices:', error);
    } finally {
      setLoading(false);
    }
  }, [currentPage, pageSize, searchTerm]);

  // Check devices online status
  const checkDevicesOnlineStatus = async (deviceList) => {
    try {
      const response = await axios.get('/api/device/get-connections');
      console.log('IoT Management: MQTT clients response:', response.data);

      if (response.data.success && response.data.clients) {
        console.log('IoT Management: Total MQTT clients:', response.data.clients.length);
        console.log('IoT Management: Sample clients:', response.data.clients.slice(0, 5));

        const onlineDeviceNumbers = new Set(
          response.data.clients
            .filter(client => client.connected)
            .map(client => client.clientid)
        );

        console.log('IoT Management: Online device numbers:', Array.from(onlineDeviceNumbers));
        console.log('IoT Management: Device list device numbers:', deviceList.map(d => d.deviceNumber));

        setOnlineDevices(onlineDeviceNumbers);
        setLastStatusUpdate(new Date());

        // Update devices with online status
        const updatedDevices = deviceList.map(device => ({
          ...device,
          isOnline: onlineDeviceNumbers.has(device.deviceNumber)
        }));

        setDevices(updatedDevices);
      }
    } catch (error) {
      console.error('IoT Management: Error checking online status:', error);
    }
  };

  // Load devices on component mount
  useEffect(() => {
    loadDevices();
  }, [loadDevices]);

  // Auto-refresh online status every 30 seconds
  useEffect(() => {
    const interval = setInterval(() => {
      if (devices.length > 0) {
        checkDevicesOnlineStatus(devices);
      }
    }, 30000); // 30 seconds

    return () => clearInterval(interval);
  }, [devices]);

  // Debounce input value to search term (for filtering)
  useEffect(() => {
    const timeoutId = setTimeout(() => {
      setSearchTerm(inputValue);
    }, 150); // Very short debounce for filtering

    return () => clearTimeout(timeoutId);
  }, [inputValue]);

  // Filter command history for selected device (most recent first)
  const deviceCommandHistory = useMemo(() => {
    if (!selectedDevice) return [];
    return commandHistory
      .filter(entry => entry.deviceNumber === selectedDevice.deviceNumber)
      .sort((a, b) => new Date(b.timestamp) - new Date(a.timestamp));
  }, [commandHistory, selectedDevice]);

  // Memoized filtering function to prevent unnecessary re-calculations
  const filteredDevices = useMemo(() => {
    let filtered = devices;

    // Apply search filter
    if (searchTerm.trim()) {
      const searchLower = searchTerm.toLowerCase();
      filtered = filtered.filter(device => {
        return (
          device.deviceNumber?.toLowerCase().includes(searchLower) ||
          device.userInfo?.phoneNumber?.toLowerCase().includes(searchLower) ||
          device.userInfo?.firstName?.toLowerCase().includes(searchLower) ||
          device.userInfo?.lastName?.toLowerCase().includes(searchLower) ||
          device.uix?.toLowerCase().includes(searchLower) ||
          device.type?.toLowerCase().includes(searchLower)
        );
      });
    }

    // Apply user status filter
    if (userStatusFilter !== 'all') {
      filtered = filtered.filter(device => {
        const userStatus = device.userInfo?.status;
        if (userStatusFilter === 'active') {
          return userStatus === 'active';
        } else if (userStatusFilter === 'expired') {
          return userStatus === 'expired' || userStatus === 'trial';
        }
        return true;
      });
    }

    // Apply online status filter
    if (onlineFilter !== 'all') {
      filtered = filtered.filter(device => {
        const isOnline = onlineDevices.has(device.deviceNumber);
        if (onlineFilter === 'online') {
          return isOnline;
        } else if (onlineFilter === 'offline') {
          return !isOnline;
        }
        return true;
      });
    }

    // Apply version filter
    if (versionFilter !== 'all') {
      filtered = filtered.filter(device => {
        const version = extractVersionFromPayload(device.deviceVersion);
        const versionCategory = categorizeVersion(version);
        return versionCategory === versionFilter;
      });
    }

    // Apply last log filter
    if (lastLogFilter !== 'all') {
      filtered = filtered.filter(device => {
        const logCategory = categorizeLastLog(device.lastLogTime);
        return logCategory === lastLogFilter;
      });
    }

    // Apply remaining days filter
    if (remainingDaysFilter !== 'all') {
      filtered = filtered.filter(device => {
        if (!device.user || device.user.length === 0) return remainingDaysFilter === 'expired';

        const remainingDays = calculateRemainingDays(device.user[0].expired);

        switch (remainingDaysFilter) {
          case 'expired':
            return remainingDays !== null && remainingDays < 0;
          case 'expiring_soon':
            return remainingDays !== null && remainingDays >= 0 && remainingDays <= 7;
          case 'active':
            return remainingDays !== null && remainingDays > 7;
          default:
            return true;
        }
      });
    }

    return filtered;
  }, [searchTerm, devices, userStatusFilter, onlineFilter, onlineDevices, versionFilter, lastLogFilter, remainingDaysFilter]);

  // Debounced server-side search for comprehensive results
  useEffect(() => {
    if (!searchTerm.trim()) return;

    const timeoutId = setTimeout(() => {
      // Only do server-side search if search term is long enough
      // and we might need more comprehensive results
      if (searchTerm.length >= 3 && filteredDevices.length < 10) {
        console.log('IoT Management: Triggering server-side search for:', searchTerm);
        setCurrentPage(1);
        loadDevices(1, searchTerm);
      }
    }, 2000); // 2 second debounce for server search

    return () => clearTimeout(timeoutId);
  }, [searchTerm]); // Remove filteredDevices.length dependency to prevent loops

  // Handle page changes
  const handlePageChange = (event, newPage) => {
    setCurrentPage(newPage);
    loadDevices(newPage, searchTerm);
  };

  // Handle page size changes
  const handlePageSizeChange = (event) => {
    const newPageSize = event.target.value;
    setPageSize(newPageSize);
    setCurrentPage(1);
    loadDevices(1, searchTerm);
  };

  // Handle user management dialog
  const handleUserManage = (userInfo) => {
    setSelectedUser(userInfo);
    setUserManageDialog(true);
    setDaysToAdd('');
  };

  // Handle PIN reset
  const handleResetPin = async () => {
    if (!selectedUser) return;

    setIsResettingPin(true);
    try {
      const response = await axios.post('/api/admin/user/reset-pin', {
        phoneNumber: selectedUser.phoneNumber
      });

      if (response.data.success) {
        enqueueSnackbar('PIN reset to 0000 successfully', { variant: 'success' });
      } else {
        enqueueSnackbar(response.data.message || 'Failed to reset PIN', { variant: 'error' });
      }
    } catch (error) {
      console.error('Error resetting PIN:', error);
      enqueueSnackbar('Error resetting PIN', { variant: 'error' });
    } finally {
      setIsResettingPin(false);
    }
  };

  // Handle expiration date extension
  const handleExtendExpiry = async () => {
    if (!selectedUser || !daysToAdd || isNaN(daysToAdd) || parseInt(daysToAdd) <= 0) {
      enqueueSnackbar('Please enter a valid number of days', { variant: 'error' });
      return;
    }

    setIsExtendingExpiry(true);
    try {
      // Calculate new expiration date
      const currentExpiry = selectedUser.expired ? new Date(selectedUser.expired) : new Date();
      const newExpiry = new Date(currentExpiry);
      newExpiry.setDate(newExpiry.getDate() + parseInt(daysToAdd));

      const response = await axios.post('/api/admin/user/extend-license', {
        expired: newExpiry.toISOString().split('T')[0],
        user: selectedUser._id,
        licenseKey: selectedUser.licenseKey || ''
      });

      if (response.data.success) {
        enqueueSnackbar(`Expiration date extended by ${daysToAdd} days successfully`, { variant: 'success' });
        setDaysToAdd('');
        // Refresh the devices list to show updated info
        loadDevices(currentPage, searchTerm);
      } else {
        enqueueSnackbar(response.data.message || 'Failed to extend expiration date', { variant: 'error' });
      }
    } catch (error) {
      console.error('Error extending expiry:', error);
      enqueueSnackbar('Error extending expiration date', { variant: 'error' });
    } finally {
      setIsExtendingExpiry(false);
    }
  };

  // Handle create new user
  const handleCreateUser = async () => {
    if (!newUserPhone.trim()) {
      enqueueSnackbar('Please enter a phone number', { variant: 'error' });
      return;
    }

    // Basic phone number validation
    if (newUserPhone.trim().length < 8) {
      enqueueSnackbar('Please enter a valid phone number', { variant: 'error' });
      return;
    }

    setIsCreatingUser(true);
    try {
      const response = await axios.post('/api/auth/register', {
        phoneNumber: newUserPhone.trim()
      });

      if (response.data.success) {
        enqueueSnackbar('User created successfully', { variant: 'success' });
        setNewUserPhone('');
        setCreateUserDialog(false);
        // Refresh the devices list
        loadDevices(currentPage, searchTerm);
      } else {
        enqueueSnackbar(response.data.message || 'Failed to create user', { variant: 'error' });
      }
    } catch (error) {
      console.error('Error creating user:', error);
      enqueueSnackbar('Error creating user', { variant: 'error' });
    } finally {
      setIsCreatingUser(false);
    }
  };

  // Handle device management dialog
  const handleDeviceManage = (device) => {
    setSelectedDeviceForEdit(device);
    setDeviceEditForm({
      deviceNumber: device.deviceNumber || '',
      type: device.type || '4g',
      uix: device.uix || 'CarV1.2'
    });
    setDeviceManageDialog(true);
  };

  // Handle device edit
  const handleEditDevice = async () => {
    if (!selectedDeviceForEdit || !deviceEditForm.deviceNumber.trim()) {
      enqueueSnackbar('Please fill in all required fields', { variant: 'error' });
      return;
    }

    setIsEditingDevice(true);
    try {
      const response = await axios.post(`/api/device/set/${selectedDeviceForEdit._id}`, {
        deviceNumber: deviceEditForm.deviceNumber.trim(),
        type: deviceEditForm.type,
        uix: deviceEditForm.uix
      });

      if (response.data.success) {
        enqueueSnackbar('Device updated successfully', { variant: 'success' });
        setDeviceManageDialog(false);
        // Refresh the devices list
        loadDevices(currentPage, searchTerm);
      } else {
        enqueueSnackbar(response.data.message || 'Failed to update device', { variant: 'error' });
      }
    } catch (error) {
      console.error('Error updating device:', error);
      enqueueSnackbar('Error updating device', { variant: 'error' });
    } finally {
      setIsEditingDevice(false);
    }
  };

  // Handle device delete
  const handleDeleteDevice = async () => {
    if (!selectedDeviceForEdit) return;

    setIsDeletingDevice(true);
    try {
      const response = await axios.post('/api/device/delete', {
        deviceNumber: selectedDeviceForEdit.deviceNumber
      });

      if (response.data.success) {
        enqueueSnackbar('Device deleted successfully', { variant: 'success' });
        setDeviceManageDialog(false);
        // Refresh the devices list
        loadDevices(currentPage, searchTerm);
      } else {
        enqueueSnackbar(response.data.message || 'Failed to delete device', { variant: 'error' });
      }
    } catch (error) {
      console.error('Error deleting device:', error);
      enqueueSnackbar('Error deleting device', { variant: 'error' });
    } finally {
      setIsDeletingDevice(false);
    }
  };

  // Subscribe to device response topics when devices change
  useEffect(() => {
    if (mqttConnected && devices.length > 0) {
      devices.forEach(device => {
        const responseTopic = `${device.deviceNumber}/msg`;
        mqttService.subscribe(responseTopic);
      });
    }
  }, [mqttConnected, devices]);

  // Send MQTT command to device
  const sendCommand = async (deviceNumber, command) => {
    try {
      console.log('IoT Management: Attempting to send command:', {
        deviceNumber,
        command,
        mqttConnected,
        serviceConnected: mqttService.isConnected
      });

      if (!mqttConnected) {
        throw new Error('MQTT not connected');
      }

      if (!mqttService.isConnected) {
        throw new Error(`MQTT service reports not connected. State: mqttConnected=${mqttConnected}, serviceConnected=${mqttService.isConnected}`);
      }

      const topic = deviceNumber;
      const message = {
        id: deviceNumber,
        command: command
      };

      console.log('IoT Management: Publishing to topic:', topic, 'message:', message);

      // Wait for the publish to complete
      await mqttService.publish(topic, message);

      console.log('IoT Management: Command sent successfully');

      // Add to command history
      setCommandHistory(prev => [{
        id: Date.now(),
        deviceNumber,
        type: 'command',
        message: command,
        timestamp: new Date(),
        success: true
      }, ...prev.slice(0, 99)]);

      return true;
    } catch (error) {
      console.error('IoT Management: Error sending command:', error);

      // Add failed command to history
      setCommandHistory(prev => [{
        id: Date.now(),
        deviceNumber,
        type: 'command',
        message: command,
        timestamp: new Date(),
        success: false,
        error: error.message
      }, ...prev.slice(0, 99)]);

      throw error;
    }
  };

  // Handle command execution
  const executeCommand = async () => {
    if (!selectedDevice) return;

    try {
      setCommandLoading(true);
      let command = '';

      switch (commandType) {
        case 'check':
          command = 'check';
          break;
        case 'update':
          command = 'update';
          break;
        case 'restart':
          command = 'restart';
          break;
        case 'sim':
          command = simCommand || 'sim';
          break;
        case 'custom':
          command = customCommand;
          break;
        default:
          throw new Error('Invalid command type');
      }

      if (!command.trim()) {
        throw new Error('Command cannot be empty');
      }

      await sendCommand(selectedDevice.deviceNumber, command);

      // Switch to history tab to show the command result
      setDialogTab(1);

      // Reset command states but keep dialog open to show history
      setCommandType('');
      setSimCommand('');
      setCustomCommand('');
      
    } catch (error) {
      console.error('IoT Management: Command execution failed:', error);
      alert(`Command failed: ${error.message}`);
    } finally {
      setCommandLoading(false);
    }
  };

  return (
    <Page title="IoT Device Management">
      <Layout />
      <Container maxWidth="xl" sx={{ py: 3 }}>
        {/* Header */}
        <Box sx={{ mb: 3 }}>
          <Typography variant="h4" gutterBottom>
            IoT Device Management
          </Typography>
          <Typography variant="body2" color="text.secondary">
            Send update, restart, and SIM commands to IoT devices via MQTT
          </Typography>
        </Box>

        {/* Status and Controls */}
        <Grid container spacing={3} sx={{ mb: 3 }}>
          <Grid item xs={12} md={6}>
            <Card>
              <CardContent>
                <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
                  <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                    {mqttConnected ? (
                      <OnlineIcon color="success" />
                    ) : (
                      <OfflineIcon color="error" />
                    )}
                    <Typography variant="body2">
                      MQTT: {mqttConnected ? 'Connected' : 'Disconnected'}
                    </Typography>
                  </Box>
                  <Divider orientation="vertical" flexItem />
                  <Typography variant="body2">
                    Devices: {devices.length} total, {onlineDevices.size} online
                  </Typography>
                </Box>
              </CardContent>
            </Card>
          </Grid>
          <Grid item xs={12} md={6}>
            <Box sx={{ display: 'flex', gap: 2, height: '100%', alignItems: 'center' }}>
              <TextField
                fullWidth
                size="small"
                placeholder="Search by device number, phone, name, type... (instant filter)"
                value={inputValue}
                onChange={(e) => setInputValue(e.target.value)}
                InputProps={{
                  startAdornment: (
                    <InputAdornment position="start">
                      <SearchIcon color={inputValue ? 'primary' : 'action'} />
                    </InputAdornment>
                  ),
                  endAdornment: inputValue && (
                    <InputAdornment position="end">
                      <IconButton
                        size="small"
                        onClick={() => {
                          setInputValue('');
                          setSearchTerm('');
                        }}
                        edge="end"
                        title="Clear search"
                      >
                        <ClearIcon />
                      </IconButton>
                    </InputAdornment>
                  ),
                }}
              />
              <Button
                variant="contained"
                color="primary"
                onClick={() => setCreateUserDialog(true)}
                sx={{ mr: 1 }}
              >
                Create User
              </Button>
              <Button
                variant="outlined"
                startIcon={<RefreshIcon />}
                onClick={() => {
                  setCurrentPage(1);
                  loadDevices(1, searchTerm);
                }}
                disabled={loading}
              >
                Refresh
              </Button>
            </Box>
          </Grid>
        </Grid>

        {/* Filter Controls */}
        <Grid container spacing={2} sx={{ mb: 2 }}>
          <Grid item xs={12} sm={6} md={2.4}>
            <FormControl fullWidth size="small">
              <InputLabel>User Status</InputLabel>
              <Select
                value={userStatusFilter}
                onChange={(e) => setUserStatusFilter(e.target.value)}
                label="User Status"
              >
                <MenuItem value="all">All Users</MenuItem>
                <MenuItem value="active">Active Only</MenuItem>
                <MenuItem value="expired">Expired/Trial Only</MenuItem>
              </Select>
            </FormControl>
          </Grid>
          <Grid item xs={12} sm={6} md={2.4}>
            <FormControl fullWidth size="small">
              <InputLabel>Device Status</InputLabel>
              <Select
                value={onlineFilter}
                onChange={(e) => setOnlineFilter(e.target.value)}
                label="Device Status"
              >
                <MenuItem value="all">All Devices</MenuItem>
                <MenuItem value="online">Online Only</MenuItem>
                <MenuItem value="offline">Offline Only</MenuItem>
              </Select>
            </FormControl>
          </Grid>
          <Grid item xs={12} sm={6} md={2.4}>
            <FormControl fullWidth size="small">
              <InputLabel>Remaining Days</InputLabel>
              <Select
                value={remainingDaysFilter}
                onChange={(e) => setRemainingDaysFilter(e.target.value)}
                label="Remaining Days"
              >
                <MenuItem value="all">All Users</MenuItem>
                <MenuItem value="expired">Expired</MenuItem>
                <MenuItem value="expiring_soon">Expiring Soon (≤7 days)</MenuItem>
                <MenuItem value="active">Active (>7 days)</MenuItem>
              </Select>
            </FormControl>
          </Grid>
          <Grid item xs={12} sm={6} md={2.4}>
            <FormControl fullWidth size="small">
              <InputLabel>Version</InputLabel>
              <Select
                value={versionFilter}
                onChange={(e) => setVersionFilter(e.target.value)}
                label="Version"
              >
                <MenuItem value="all">All Versions</MenuItem>
                <MenuItem value="unknown">Unknown</MenuItem>
                <MenuItem value="old">Old (1.2.6-1.3.x)</MenuItem>
                <MenuItem value="current">Current (1.4.0+)</MenuItem>
              </Select>
            </FormControl>
          </Grid>
          <Grid item xs={12} sm={6} md={2.4}>
            <FormControl fullWidth size="small">
              <InputLabel>Last Log</InputLabel>
              <Select
                value={lastLogFilter}
                onChange={(e) => setLastLogFilter(e.target.value)}
                label="Last Log"
              >
                <MenuItem value="all">All Logs</MenuItem>
                <MenuItem value="no_log">No Log/Old (>1 week)</MenuItem>
                <MenuItem value="old">Old (1-7 days)</MenuItem>
                <MenuItem value="recent">Recent (24 hours)</MenuItem>
              </Select>
            </FormControl>
          </Grid>
        </Grid>

        {/* Filter Summary */}
        <Grid container spacing={2} sx={{ mb: 2 }}>
          <Grid item xs={12}>
            <Box sx={{ display: 'flex', gap: 1, alignItems: 'center', flexWrap: 'wrap' }}>
              <Chip
                label={`Total: ${filteredDevices.length}`}
                color="primary"
                variant="outlined"
                size="small"
              />
              {userStatusFilter !== 'all' && (
                <Chip
                  label={`${userStatusFilter === 'active' ? 'Active' : 'Expired/Trial'} Users`}
                  color="secondary"
                  variant="outlined"
                  size="small"
                  onDelete={() => setUserStatusFilter('all')}
                />
              )}
              {onlineFilter !== 'all' && (
                <Chip
                  label={`${onlineFilter === 'online' ? 'Online' : 'Offline'} Devices`}
                  color="info"
                  variant="outlined"
                  size="small"
                  onDelete={() => setOnlineFilter('all')}
                />
              )}
              {versionFilter !== 'all' && (
                <Chip
                  label={`Version: ${versionFilter === 'unknown' ? 'Unknown' : versionFilter === 'old' ? 'Old (1.2.6-1.3.x)' : 'Current (1.4.0+)'}`}
                  color="warning"
                  variant="outlined"
                  size="small"
                  onDelete={() => setVersionFilter('all')}
                />
              )}
              {lastLogFilter !== 'all' && (
                <Chip
                  label={`Log: ${lastLogFilter === 'no_log' ? 'No Log/Old' : lastLogFilter === 'old' ? 'Old (1-7 days)' : 'Recent (24h)'}`}
                  color="success"
                  variant="outlined"
                  size="small"
                  onDelete={() => setLastLogFilter('all')}
                />
              )}
              {remainingDaysFilter !== 'all' && (
                <Chip
                  label={`Days: ${remainingDaysFilter === 'expired' ? 'Expired' : remainingDaysFilter === 'expiring_soon' ? 'Expiring Soon' : 'Active'}`}
                  color="info"
                  variant="outlined"
                  size="small"
                  onDelete={() => setRemainingDaysFilter('all')}
                />
              )}
              {(userStatusFilter !== 'all' || onlineFilter !== 'all' || versionFilter !== 'all' || lastLogFilter !== 'all' || remainingDaysFilter !== 'all') && (
                <Chip
                  label="Clear All Filters"
                  color="error"
                  variant="outlined"
                  size="small"
                  onClick={() => {
                    setUserStatusFilter('all');
                    setOnlineFilter('all');
                    setVersionFilter('all');
                    setLastLogFilter('all');
                    setRemainingDaysFilter('all');
                  }}
                />
              )}
            </Box>
          </Grid>
        </Grid>

        {/* Device List */}
        <Grid container spacing={3}>
          <Grid item xs={12}>
            <Card>
              <CardContent>
                <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>
                  <Typography variant="h6">
                    Device List ({filteredDevices.length} {searchTerm ? 'filtered' : 'total'}
                    {searchTerm && ` of ${totalDevices}`})
                  </Typography>
                  <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                    <Chip
                      label={mqttConnected ? 'MQTT Connected' : 'MQTT Disconnected'}
                      size="small"
                      color={mqttConnected ? 'success' : 'error'}
                      variant="filled"
                    />
                    {!loading && totalDevices > 0 && (
                      <>
                        <Chip
                          label={`${onlineDevices.size} online`}
                          size="small"
                          color="success"
                          variant="outlined"
                        />
                        <Chip
                          label={`${totalDevices - onlineDevices.size} offline`}
                          size="small"
                          color="error"
                          variant="outlined"
                        />
                        <Chip
                          label={`${totalDevices} total`}
                          size="small"
                          color="primary"
                          variant="outlined"
                        />
                      </>
                    )}
                    <Tooltip title="Refresh online status">
                      <IconButton
                        size="small"
                        onClick={() => checkDevicesOnlineStatus(devices)}
                        disabled={loading}
                      >
                        <RefreshIcon fontSize="small" />
                      </IconButton>
                    </Tooltip>
                  </Box>
                </Box>
                {lastStatusUpdate && (
                  <Typography variant="caption" color="text.secondary" sx={{ mb: 1, display: 'block' }}>
                    Status last updated: {formatDistanceToNow(lastStatusUpdate, { addSuffix: true })}
                  </Typography>
                )}
                {loading ? (
                  <Box sx={{ display: 'flex', justifyContent: 'center', p: 3 }}>
                    <CircularProgress />
                  </Box>
                ) : (
                  <Scrollbar>
                    <TableContainer>
                      <Table size="small">
                        <TableHead>
                          <TableRow>
                            <TableCell>Device Number</TableCell>
                            <TableCell>User</TableCell>
                            <TableCell>Remaining Days</TableCell>
                            <TableCell>Type</TableCell>
                            <TableCell>Status</TableCell>
                            <TableCell>Version</TableCell>
                            <TableCell>Last Log</TableCell>
                            <TableCell>SIM Info</TableCell>
                            <TableCell>Actions</TableCell>
                          </TableRow>
                        </TableHead>
                        <TableBody>
                          {filteredDevices.map((device) => (
                            <TableRow key={device.deviceNumber}>
                              <TableCell>
                                <Typography
                                  variant="body2"
                                  fontWeight="medium"
                                  sx={{
                                    cursor: 'pointer',
                                    color: 'primary.main',
                                    '&:hover': { textDecoration: 'underline' }
                                  }}
                                  onClick={() => handleDeviceManage(device)}
                                >
                                  {device.deviceNumber}
                                </Typography>
                                {device.phoneNumber && (
                                  <Typography variant="caption" color="text.secondary">
                                    {device.phoneNumber}
                                  </Typography>
                                )}
                              </TableCell>
                              <TableCell>
                                {device.userInfo ? (
                                  <Box>
                                    <Typography
                                      variant="body2"
                                      sx={{
                                        cursor: 'pointer',
                                        color: 'primary.main',
                                        '&:hover': { textDecoration: 'underline' }
                                      }}
                                      onClick={() => handleUserManage(device.userInfo)}
                                    >
                                      {device.userInfo.phoneNumber}
                                    </Typography>
                                    <Typography variant="caption" color="text.secondary">
                                      {device.userInfo.status || 'active'}
                                    </Typography>
                                  </Box>
                                ) : (
                                  <Typography variant="caption" color="text.secondary">
                                    No user
                                  </Typography>
                                )}
                              </TableCell>
                              <TableCell>
                                {(() => {
                                  if (!device.user || device.user.length === 0) {
                                    return (
                                      <Chip
                                        size="small"
                                        label="No User"
                                        color="default"
                                        variant="outlined"
                                      />
                                    );
                                  }

                                  const userExpired = device.user[0].expired;
                                  const remainingInfo = getRemainingDaysDisplay(userExpired);

                                  return (
                                    <Chip
                                      size="small"
                                      label={remainingInfo.text}
                                      color={remainingInfo.color}
                                      variant="outlined"
                                    />
                                  );
                                })()}
                              </TableCell>
                              <TableCell>
                                <Chip
                                  size="small"
                                  label={device.uix || 'Unknown'}
                                  color={device.type === '4g' ? 'primary' : 'default'}
                                />
                              </TableCell>
                              <TableCell>
                                <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                                  {device.isOnline ? (
                                    <OnlineIcon color="success" fontSize="small" />
                                  ) : (
                                    <OfflineIcon color="error" fontSize="small" />
                                  )}
                                  <Typography variant="body2">
                                    {device.isOnline ? 'Online' : 'Offline'}
                                  </Typography>
                                </Box>
                              </TableCell>
                              <TableCell>
                                {(() => {
                                  const version = extractVersionFromPayload(device.deviceVersion);
                                  const category = categorizeVersion(version);
                                  const getVersionColor = () => {
                                    switch (category) {
                                      case 'current': return 'success';
                                      case 'old': return 'warning';
                                      case 'unknown': return 'error';
                                      default: return 'default';
                                    }
                                  };

                                  return (
                                    <Chip
                                      size="small"
                                      label={version}
                                      color={getVersionColor()}
                                      variant="outlined"
                                    />
                                  );
                                })()}
                              </TableCell>
                              <TableCell>
                                {(() => {
                                  const logCategory = categorizeLastLog(device.lastLogTime);
                                  const getLogColor = () => {
                                    switch (logCategory) {
                                      case 'recent': return 'success';
                                      case 'old': return 'warning';
                                      case 'no_log': return 'error';
                                      default: return 'default';
                                    }
                                  };

                                  const logText = device.lastLogTime
                                    ? formatDistanceToNow(new Date(device.lastLogTime), { addSuffix: true })
                                    : 'Never';

                                  return (
                                    <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                                      <Chip
                                        size="small"
                                        label={logCategory === 'recent' ? 'Recent' : logCategory === 'old' ? 'Old' : 'No Log'}
                                        color={getLogColor()}
                                        variant="outlined"
                                      />
                                      <Typography variant="caption" color="text.secondary">
                                        {logText}
                                      </Typography>
                                    </Box>
                                  );
                                })()}
                              </TableCell>
                              <TableCell>
                                {device.simInfo ? (
                                  <Box>
                                    {device.simInfo.balance && (
                                      <Typography variant="body2" color="primary">
                                        {device.simInfo.balance} ₮
                                      </Typography>
                                    )}
                                    {device.simInfo.expired && (
                                      <Typography variant="caption" color="text.secondary">
                                        Exp: {new Date(device.simInfo.expired).toLocaleDateString()}
                                      </Typography>
                                    )}
                                    {!device.simInfo.balance && !device.simInfo.expired && device.simInfo.content && (
                                      <Typography variant="caption" color="text.secondary" title={device.simInfo.content}>
                                        {device.simInfo.content.substring(0, 20)}...
                                      </Typography>
                                    )}
                                  </Box>
                                ) : (
                                  <Typography variant="caption" color="text.secondary">
                                    No SIM data
                                  </Typography>
                                )}
                              </TableCell>
                              <TableCell>
                                <Box sx={{ display: 'flex', gap: 1 }}>
                                  <Tooltip title="Check Status">
                                    <IconButton
                                      size="small"
                                      onClick={() => {
                                        setSelectedDevice(device);
                                        setCommandType('check');
                                        setDialogTab(0);
                                        setCommandDialog(true);
                                      }}
                                    >
                                      <CheckIcon fontSize="small" />
                                    </IconButton>
                                  </Tooltip>
                                  <Tooltip title="Update">
                                    <IconButton
                                      size="small"
                                      onClick={() => {
                                        setSelectedDevice(device);
                                        setCommandType('update');
                                        setDialogTab(0);
                                        setCommandDialog(true);
                                      }}
                                    >
                                      <UpdateIcon fontSize="small" />
                                    </IconButton>
                                  </Tooltip>
                                  <Tooltip title="Restart">
                                    <IconButton
                                      size="small"
                                      onClick={() => {
                                        setSelectedDevice(device);
                                        setCommandType('restart');
                                        setDialogTab(0);
                                        setCommandDialog(true);
                                      }}
                                    >
                                      <RestartIcon fontSize="small" />
                                    </IconButton>
                                  </Tooltip>
                                  <Tooltip title="SIM Command">
                                    <IconButton
                                      size="small"
                                      onClick={() => {
                                        setSelectedDevice(device);
                                        setCommandType('sim');
                                        setDialogTab(0);
                                        setCommandDialog(true);
                                      }}
                                    >
                                      <SimIcon fontSize="small" />
                                    </IconButton>
                                  </Tooltip>
                                </Box>
                              </TableCell>
                            </TableRow>
                          ))}
                        </TableBody>
                      </Table>
                    </TableContainer>
                  </Scrollbar>
                )}

                {/* Pagination Controls */}
                {!loading && totalDevices > 0 && (
                  <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mt: 2, px: 2, pb: 2 }}>
                    <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
                      <Typography variant="body2" color="text.secondary">
                        Showing {((currentPage - 1) * pageSize) + 1} to {Math.min(currentPage * pageSize, totalDevices)} of {totalDevices} devices
                      </Typography>
                      <FormControl size="small" sx={{ minWidth: 80 }}>
                        <InputLabel>Per page</InputLabel>
                        <Select
                          value={pageSize}
                          label="Per page"
                          onChange={handlePageSizeChange}
                        >
                          <MenuItem value={10}>10</MenuItem>
                          <MenuItem value={20}>20</MenuItem>
                          <MenuItem value={50}>50</MenuItem>
                          <MenuItem value={100}>100</MenuItem>
                        </Select>
                      </FormControl>
                    </Box>
                    <Pagination
                      count={totalPages}
                      page={currentPage}
                      onChange={handlePageChange}
                      color="primary"
                      showFirstButton
                      showLastButton
                    />
                  </Box>
                )}
              </CardContent>
            </Card>
          </Grid>
        </Grid>

        {/* Command Dialog */}
        <Dialog open={commandDialog} onClose={() => setCommandDialog(false)} maxWidth="md" fullWidth>
          <DialogTitle>
            Device {selectedDevice?.deviceNumber} - Actions & History
          </DialogTitle>
          <DialogContent>
            <Box sx={{ borderBottom: 1, borderColor: 'divider', mb: 2 }}>
              <Tabs value={dialogTab} onChange={(e, newValue) => setDialogTab(newValue)}>
                <Tab label="Send Command" />
                <Tab label={`History (${deviceCommandHistory.length})`} />
              </Tabs>
            </Box>

            {dialogTab === 0 && (
              <Stack spacing={3}>
                {!mqttConnected && (
                  <Alert severity="warning">
                    MQTT is not connected. Please check your connection.
                  </Alert>
                )}

                <FormControl fullWidth>
                  <InputLabel>Command Type</InputLabel>
                  <Select
                    value={commandType}
                    label="Command Type"
                    onChange={(e) => setCommandType(e.target.value)}
                  >
                    <MenuItem value="check">Check Status</MenuItem>
                    <MenuItem value="update">Update</MenuItem>
                    <MenuItem value="restart">Restart</MenuItem>
                    <MenuItem value="sim">SIM Command</MenuItem>
                    <MenuItem value="custom">Custom Command</MenuItem>
                  </Select>
                </FormControl>

                {commandType === 'sim' && (
                  <TextField
                    fullWidth
                    label="SIM Command"
                    value={simCommand}
                    onChange={(e) => setSimCommand(e.target.value)}
                    placeholder="Enter SIM command (e.g., sim_status, sim_info)"
                    helperText="Leave empty to send default 'sim' command"
                  />
                )}

                {commandType === 'custom' && (
                  <TextField
                    fullWidth
                    label="Custom Command"
                    value={customCommand}
                    onChange={(e) => setCustomCommand(e.target.value)}
                    placeholder="Enter custom command"
                    required
                  />
                )}

                {commandType && commandType !== 'custom' && commandType !== 'sim' && (
                  <Alert severity="info">
                    {commandType === 'check'
                      ? 'This will request the current status and information from the device.'
                      : `This will send a "${commandType}" command to the device.`
                    }
                  </Alert>
                )}
              </Stack>
            )}

            {dialogTab === 1 && (
              <Box sx={{ minHeight: 300, maxHeight: 500 }}>
                <Scrollbar>
                  <Stack spacing={2}>
                    {deviceCommandHistory.length === 0 ? (
                      <Box sx={{ textAlign: 'center', py: 4 }}>
                        <HistoryIcon sx={{ fontSize: 48, color: 'text.secondary', mb: 2 }} />
                        <Typography variant="body2" color="text.secondary">
                          No commands sent to this device yet
                        </Typography>
                      </Box>
                    ) : (
                      deviceCommandHistory.map((entry) => (
                        <Paper key={entry.id} variant="outlined" sx={{ p: 2 }}>
                          <Box sx={{ display: 'flex', alignItems: 'center', gap: 1, mb: 1 }}>
                            <Chip
                              size="small"
                              label={entry.type}
                              color={entry.type === 'command' ? 'primary' : 'secondary'}
                            />
                            <Typography variant="caption" color="text.secondary">
                              {formatDistanceToNow(entry.timestamp, { addSuffix: true })}
                            </Typography>
                            {entry.success ? (
                              <OnlineIcon color="success" fontSize="small" />
                            ) : (
                              <OfflineIcon color="error" fontSize="small" />
                            )}
                          </Box>
                          <Typography variant="body2" fontWeight="medium" sx={{ mb: 1 }}>
                            Command: {entry.message}
                          </Typography>
                          {entry.error && (
                            <Typography variant="caption" color="error">
                              Error: {entry.error}
                            </Typography>
                          )}
                        </Paper>
                      ))
                    )}
                  </Stack>
                </Scrollbar>
              </Box>
            )}
          </DialogContent>
          <DialogActions>
            <Button onClick={() => setCommandDialog(false)}>
              Close
            </Button>
            {dialogTab === 0 && (
              <LoadingButton
                variant="contained"
                onClick={executeCommand}
                loading={commandLoading}
                disabled={!mqttConnected || !commandType || (commandType === 'custom' && !customCommand.trim())}
                startIcon={<SendIcon />}
              >
                Send Command
              </LoadingButton>
            )}
          </DialogActions>
        </Dialog>

        {/* User Management Dialog */}
        <Dialog
          open={userManageDialog}
          onClose={() => setUserManageDialog(false)}
          maxWidth="sm"
          fullWidth
        >
          <DialogTitle>
            User Management - {selectedUser?.phoneNumber}
          </DialogTitle>
          <DialogContent>
            <Box sx={{ pt: 2 }}>
              {selectedUser && (
                <Box sx={{ mb: 3 }}>
                  <Typography variant="body2" color="text.secondary" gutterBottom>
                    User Information
                  </Typography>
                  <Typography variant="body1">
                    Phone: {selectedUser.phoneNumber}
                  </Typography>
                  <Typography variant="body1">
                    Status: {selectedUser.status || 'active'}
                  </Typography>
                  {selectedUser.expired && (
                    <Typography variant="body1">
                      Expires: {new Date(selectedUser.expired).toLocaleDateString()}
                    </Typography>
                  )}
                </Box>
              )}

              <Divider sx={{ my: 2 }} />

              {/* PIN Reset Section */}
              <Box sx={{ mb: 3 }}>
                <Typography variant="h6" gutterBottom>
                  Reset PIN Code
                </Typography>
                <Typography variant="body2" color="text.secondary" sx={{ mb: 2 }}>
                  Reset user's PIN code to 0000
                </Typography>
                <LoadingButton
                  variant="outlined"
                  color="warning"
                  onClick={handleResetPin}
                  loading={isResettingPin}
                  fullWidth
                >
                  Reset PIN to 0000
                </LoadingButton>
              </Box>

              <Divider sx={{ my: 2 }} />

              {/* Extend Expiration Section */}
              <Box>
                <Typography variant="h6" gutterBottom>
                  Extend Expiration Date
                </Typography>
                <Typography variant="body2" color="text.secondary" sx={{ mb: 2 }}>
                  Add days to the user's expiration date
                </Typography>
                <TextField
                  fullWidth
                  type="number"
                  label="Days to Add"
                  value={daysToAdd}
                  onChange={(e) => setDaysToAdd(e.target.value)}
                  placeholder="Enter number of days (e.g., 30)"
                  sx={{ mb: 2 }}
                  inputProps={{ min: 1 }}
                />
                <LoadingButton
                  variant="contained"
                  color="primary"
                  onClick={handleExtendExpiry}
                  loading={isExtendingExpiry}
                  disabled={!daysToAdd || isNaN(daysToAdd) || parseInt(daysToAdd) <= 0}
                  fullWidth
                >
                  Extend Expiration
                </LoadingButton>
              </Box>
            </Box>
          </DialogContent>
          <DialogActions>
            <Button onClick={() => setUserManageDialog(false)}>
              Close
            </Button>
          </DialogActions>
        </Dialog>

        {/* Create New User Dialog */}
        <Dialog
          open={createUserDialog}
          onClose={() => setCreateUserDialog(false)}
          maxWidth="sm"
          fullWidth
        >
          <DialogTitle>Create New User</DialogTitle>
          <DialogContent>
            <Box sx={{ pt: 2 }}>
              <TextField
                fullWidth
                label="Phone Number"
                value={newUserPhone}
                onChange={(e) => setNewUserPhone(e.target.value)}
                placeholder="Enter phone number"
                inputProps={{
                  pattern: "[0-9]*",
                  inputMode: "numeric"
                }}
              />
            </Box>
          </DialogContent>
          <DialogActions>
            <Button onClick={() => setCreateUserDialog(false)}>
              Cancel
            </Button>
            <LoadingButton
              variant="contained"
              onClick={handleCreateUser}
              loading={isCreatingUser}
              disabled={!newUserPhone.trim()}
            >
              Create User
            </LoadingButton>
          </DialogActions>
        </Dialog>

        {/* Device Management Dialog */}
        <Dialog
          open={deviceManageDialog}
          onClose={() => setDeviceManageDialog(false)}
          maxWidth="sm"
          fullWidth
        >
          <DialogTitle>
            Device Management - {selectedDeviceForEdit?.deviceNumber}
          </DialogTitle>
          <DialogContent>
            <Box sx={{ pt: 2 }}>
              {selectedDeviceForEdit && (
                <Box sx={{ mb: 3 }}>
                  <Typography variant="body2" color="text.secondary" gutterBottom>
                    Device Information
                  </Typography>
                  <Typography variant="body1">
                    Device Number: {selectedDeviceForEdit.deviceNumber}
                  </Typography>
                  <Typography variant="body1">
                    Type: {selectedDeviceForEdit.type}
                  </Typography>
                  <Typography variant="body1">
                    UIX: {selectedDeviceForEdit.uix}
                  </Typography>
                  {selectedDeviceForEdit.userInfo && (
                    <Typography variant="body1">
                      User: {selectedDeviceForEdit.userInfo.phoneNumber}
                    </Typography>
                  )}
                </Box>
              )}

              <Divider sx={{ my: 2 }} />

              {/* Edit Device Section */}
              <Box sx={{ mb: 3 }}>
                <Typography variant="h6" gutterBottom>
                  Edit Device
                </Typography>
                <Stack spacing={2}>
                  <TextField
                    fullWidth
                    label="Device Number"
                    value={deviceEditForm.deviceNumber}
                    onChange={(e) => setDeviceEditForm(prev => ({ ...prev, deviceNumber: e.target.value }))}
                  />
                  <FormControl fullWidth>
                    <InputLabel>Device Type</InputLabel>
                    <Select
                      value={deviceEditForm.type}
                      onChange={(e) => setDeviceEditForm(prev => ({ ...prev, type: e.target.value }))}
                      label="Device Type"
                    >
                      <MenuItem value="4g">4G Net</MenuItem>
                      <MenuItem value="sms">SMS</MenuItem>
                    </Select>
                  </FormControl>
                  <FormControl fullWidth>
                    <InputLabel>UIX Type</InputLabel>
                    <Select
                      value={deviceEditForm.uix}
                      onChange={(e) => setDeviceEditForm(prev => ({ ...prev, uix: e.target.value }))}
                      label="UIX Type"
                    >
                      <MenuItem value="CarV1.2">CarV1.2</MenuItem>
                      <MenuItem value="Car2.2">Car2.2</MenuItem>
                      <MenuItem value="Chip">Chip</MenuItem>
                    </Select>
                  </FormControl>
                  <LoadingButton
                    variant="contained"
                    color="primary"
                    onClick={handleEditDevice}
                    loading={isEditingDevice}
                    disabled={!deviceEditForm.deviceNumber.trim()}
                    fullWidth
                  >
                    Update Device
                  </LoadingButton>
                </Stack>
              </Box>

              <Divider sx={{ my: 2 }} />

              {/* Delete Device Section */}
              <Box>
                <Typography variant="h6" gutterBottom color="error">
                  Delete Device
                </Typography>
                <Typography variant="body2" color="text.secondary" sx={{ mb: 2 }}>
                  This action cannot be undone. The device will be permanently removed.
                </Typography>
                <LoadingButton
                  variant="outlined"
                  color="error"
                  onClick={handleDeleteDevice}
                  loading={isDeletingDevice}
                  fullWidth
                >
                  Delete Device
                </LoadingButton>
              </Box>
            </Box>
          </DialogContent>
          <DialogActions>
            <Button onClick={() => setDeviceManageDialog(false)}>
              Close
            </Button>
          </DialogActions>
        </Dialog>
      </Container>
    </Page>
  );
};

export default IoTDeviceManagement;
